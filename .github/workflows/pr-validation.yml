name: PR Validation

on:
  pull_request:
    branches: [ "develop", "feat/*" ]
    types: [ "opened", "reopened", "synchronize" ]

jobs:
  validate-pr:
    runs-on: ubuntu-latest
    steps:
      - name: Check PR title
        uses: amannn/action-semantic-pull-request@v5
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          types: |
            feat
            fix
            docs
            chore
            refactor
            test
            ci
          requireScope: false

      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Fetch all history for detecting changes

      - name: Merge with target branch
        run: |
          git config --global user.name "GitHub Actions"
          git config --global user.email "<EMAIL>"
          git fetch origin ${{ github.base_ref }}
          git merge origin/${{ github.base_ref }}

      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          java-version: '17'
          distribution: 'temurin'
          cache: gradle  # This enables Gradle caching

      - name: Grant execute permission for gradlew
        run: chmod +x gradlew

      - name: Check Gradle build scripts dependencies are sorted
        run: ./gradlew buildEnvironment --dry-run

      - name: Find affected modules
        id: affected-modules
        run: |
          # Determine the base commit to compare against
          BASE_BRANCH="origin/${{ github.base_ref }}"

          echo "Comparing against base: $BASE_BRANCH"

          CHANGED_FILES=$(git diff --name-only $BASE_BRANCH...HEAD)

          # Create a list of affected modules
          AFFECTED_MODULES=""

          # Check for changes in each module directory
          for dir in core android integrations/*; do
            if [ -d "$dir" ] && echo "$CHANGED_FILES" | grep -q "^$dir/"; then
              MODULE_PATH=$(echo $dir | sed 's/\//:/')
              if [ -z "$AFFECTED_MODULES" ]; then
                AFFECTED_MODULES=":$MODULE_PATH"
              else
                AFFECTED_MODULES="$AFFECTED_MODULES,:$MODULE_PATH"
              fi
              echo "$dir=true" >> $GITHUB_OUTPUT
            else
              if [ -d "$dir" ]; then
                echo "$dir=false" >> $GITHUB_OUTPUT
              fi
            fi
          done

          echo "affected_modules=$AFFECTED_MODULES" >> $GITHUB_OUTPUT
          echo "Affected modules: $AFFECTED_MODULES"

      - name: Run Detekt on affected modules
        run: |
          AFFECTED_MODULES="${{ steps.affected-modules.outputs.affected_modules }}"
          if [ -n "$AFFECTED_MODULES" ]; then
            IFS=',' read -ra MODULES <<< "$AFFECTED_MODULES"
            for module in "${MODULES[@]}"; do
              echo "Running detekt for $module"
              ./gradlew ${module}:detekt
            done
          else
            echo "No modules affected, skipping detekt"
          fi

      - name: Run Android Lint on affected modules
        run: |
          AFFECTED_MODULES="${{ steps.affected-modules.outputs.affected_modules }}"
          if [ -n "$AFFECTED_MODULES" ]; then
            IFS=',' read -ra MODULES <<< "$AFFECTED_MODULES"
            for module in "${MODULES[@]}"; do
              # Skip core module for lint
              if [[ "$module" != ":core" ]]; then
                echo "Running lint for $module"
                ./gradlew ${module}:lint
              fi
            done
          else
            echo "No modules affected, skipping lint"
          fi

      - name: Run SonarCloud Analysis
        uses: SonarSource/sonarqube-scan-action@2500896589ef8f7247069a56136f8dc177c27ccf  # v5.2.0
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}

      - name: Run tests on affected modules
        run: |
          AFFECTED_MODULES="${{ steps.affected-modules.outputs.affected_modules }}"
          if [ -n "$AFFECTED_MODULES" ]; then
            IFS=',' read -ra MODULES <<< "$AFFECTED_MODULES"
            for module in "${MODULES[@]}"; do
              echo "Running tests for $module"
              ./gradlew ${module}:test
            done
          else
            echo "No modules affected, skipping tests"
          fi

      - name: Build affected modules
        run: |
          AFFECTED_MODULES="${{ steps.affected-modules.outputs.affected_modules }}"
          if [ -n "$AFFECTED_MODULES" ]; then
            IFS=',' read -ra MODULES <<< "$AFFECTED_MODULES"
            for module in "${MODULES[@]}"; do
              echo "Building $module"
              ./gradlew ${module}:build
            done
          else
            echo "No modules affected, skipping build"
          fi

      - name: Build Library Module AARs
        run: |
          echo "Building AAR files for library modules..."
          # Build core module (generates JAR)
          ./gradlew :core:assembleRelease
          # Build android module (generates AAR)
          ./gradlew :android:assembleRelease
          # Build integration modules (generate AARs)
          ./gradlew :integrations:adjust:assembleRelease
          ./gradlew :integrations:firebase:assembleRelease
          ./gradlew :integrations:facebook:assembleRelease
          ./gradlew :integrations:braze:assembleRelease

          echo "Listing generated artifacts..."
          find . -name "*.aar" -o -name "*-release.jar" | grep -E "(core|android|integrations)" | head -20

      - name: Upload Library AARs
        uses: actions/upload-artifact@v4
        with:
          name: library-aars
          path: |
            core/build/libs/*-release.jar
            android/build/outputs/aar/*-release.aar
            integrations/*/build/outputs/aar/*-release.aar
          retention-days: 30
      #
      #      - name: Build Sample App
      #        run: ./gradlew :app:assembleDebug
      #
      #      - name: Upload Sample App to BrowserStack
      #        uses: browserstack/github-actions/espresso@master
      #        with:
      #          username: ${{ secrets.BROWSERSTACK_USERNAME }}
      #          access-key: ${{ secrets.BROWSERSTACK_ACCESS_KEY }}
      #          app-path: app/build/outputs/apk/debug/app-debug.apk

      - name: Upload test reports
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: test-reports
          path: |
            **/build/reports/tests/
            **/build/reports/detekt/
            **/build/reports/lint-results*.html

      - name: Notify Slack
        if: always()
        uses: slackapi/slack-github-action@b0fa283ad8fea605de13dc3f449259339835fc52  # v2.1.0
        with:
          payload: |
            {
              "text": "PR Validation ${{ job.status == 'success' && 'passed ✅' || 'failed ❌' }}",
              "blocks": [
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "*PR Validation ${{ job.status == 'success' && 'passed ✅' || 'failed ❌' }}*\nRepository: ${{ github.repository }}\nPR: #${{ github.event.number }} - ${{ github.event.pull_request.title }}\nBranch: ${{ github.head_ref }} → ${{ github.base_ref }}\nBy: ${{ github.actor }}\n<${{ github.event.pull_request.html_url }}|View PR>"
                  }
                }
              ]
            }
          webhook-type: incoming-webhook
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}